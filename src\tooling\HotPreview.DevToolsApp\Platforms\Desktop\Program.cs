using HotPreview.DevToolsApp;
using Serilog;
using Uno.UI.Hosting;

internal class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        // Check if this is the first instance of the application
        if (!SingleInstanceManager.IsFirstInstance())
        {
            // Another instance is already running and has been activated
            return;
        }

        try
        {
            UnoPlatformHost? host = null;

            try
            {
                host = UnoPlatformHostBuilder.Create()
                    .App(() => new App())
                    .UseX11()
                    .UseLinuxFrameBuffer()
                    .UseMacOS()
                    .UseWin32()
                    .Build();
            }
            catch (Exception ex)
            {
                LogFatalExceptionAndRethrow(ex, "application initialization");
            }

            try
            {
                host?.Run();
            }
            catch (Exception ex)
            {
                LogFatalExceptionAndRethrow(ex, "application execution");
            }
        }
        finally
        {
            // Ensure logs are flushed and mutex is released when the application shuts down
            try
            {
                Log.CloseAndFlush();
            }
            catch
            {
                // Ignore any errors during log cleanup
            }

            SingleInstanceManager.ReleaseMutex();
        }
    }

    /// <summary>
    /// Logs a fatal exception and re-throws it to maintain original behavior.
    /// Tries both Serilog and console output for maximum reliability.
    /// </summary>
    /// <param name="exception">The exception to log</param>
    /// <param name="phase">The phase where the exception occurred (e.g., "application initialization")</param>
    private static void LogFatalExceptionAndRethrow(Exception exception, string phase)
    {
        string message = $"Fatal exception occurred during {phase}: {exception}";

        // Always write to console as it's the most reliable
        Console.Error.WriteLine(message);

        // Try to log with Serilog as well, but don't rely on it
        try
        {
            Log.Fatal(exception, "Fatal exception occurred during {Phase}: {Message}", phase, exception.Message);
            Log.CloseAndFlush();
        }
        catch
        {
            // Ignore logging errors - console output above is our fallback
        }

        // Re-throw to maintain original behavior and exit code
        throw exception;
    }
}
