using HotPreview.DevToolsApp;
using Serilog;
using Uno.UI.Hosting;

internal class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        // Check if this is the first instance of the application
        if (!SingleInstanceManager.IsFirstInstance())
        {
            // Another instance is already running and has been activated
            return;
        }

        try
        {
            UnoPlatformHost host = UnoPlatformHostBuilder.Create()
                .App(() => new App())
                .UseX11()
                .UseLinuxFrameBuffer()
                .UseMacOS()
                .UseWin32()
                .Build();

            host.Run();
        }
        catch (Exception ex)
        {
            LogFatalExceptionAndRethrow(ex);
        }
        finally
        {
            // Ensure logs are flushed and mutex is released when the application shuts down
            try
            {
                Log.CloseAndFlush();
            }
            catch
            {
                // Ignore any errors during log cleanup
            }

            SingleInstanceManager.ReleaseMutex();
        }
    }

    /// <summary>
    /// Logs a fatal exception and re-throws it to maintain original behavior.
    /// Tries both Serilog and console output for maximum reliability.
    /// </summary>
    /// <param name="exception">The exception to log</param>
    private static void LogFatalExceptionAndRethrow(Exception exception)
    {
        // Always write to console as it's the most reliable
        Console.Error.WriteLine($"Fatal exception occurred: {exception}");

        // Try to log with Serilog as well, but don't rely on it
        try
        {
            Log.Fatal(exception, "Fatal exception occurred: {Message}", exception.Message);
            Log.CloseAndFlush();
        }
        catch
        {
            // Ignore logging errors - console output above is our fallback
        }

        // Re-throw to maintain original behavior and exit code
        throw exception;
    }
}
