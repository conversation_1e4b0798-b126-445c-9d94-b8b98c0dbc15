using HotPreview.DevToolsApp;
using Serilog;
using Uno.UI.Hosting;

internal class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        // Check if this is the first instance of the application
        if (!SingleInstanceManager.IsFirstInstance())
        {
            // Another instance is already running and has been activated
            return;
        }

        try
        {
            UnoPlatformHost host = UnoPlatformHostBuilder.Create()
                .App(() => new App())
                .UseX11()
                .UseLinuxFrameBuffer()
                .UseMacOS()
                .UseWin32()
                .Build();

            try
            {
                host.Run();
            }
            catch (Exception ex)
            {
                // Log any exceptions that occur during host.Run()
                // Note: Logging may not be fully configured yet, so we also write to console as fallback
                try
                {
                    Log.Fatal(ex, "Fatal exception occurred during application execution: {Message}", ex.Message);
                    Log.CloseAndFlush();
                }
                catch
                {
                    // If logging fails, write to console as last resort
                    Console.Error.WriteLine($"Fatal exception occurred during application execution: {ex}");
                }

                // Re-throw to maintain original behavior and exit code
                throw;
            }
        }
        finally
        {
            // Ensure logs are flushed and mutex is released when the application shuts down
            try
            {
                Log.CloseAndFlush();
            }
            catch
            {
                // Ignore any errors during log cleanup
            }

            SingleInstanceManager.ReleaseMutex();
        }
    }
}
