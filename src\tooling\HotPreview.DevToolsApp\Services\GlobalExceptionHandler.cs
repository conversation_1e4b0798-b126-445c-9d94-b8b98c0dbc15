using System.Diagnostics;
using HotPreview.DevToolsApp.Views;
using HotPreview.Tooling.Services;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Serilog;

namespace HotPreview.DevToolsApp.Services;

/// <summary>
/// Service for handling unhandled exceptions globally across the application.
/// Logs exceptions and shows user-friendly error dialogs.
/// </summary>
public class GlobalExceptionHandler
{
    private readonly UIContextProvider _uiContextProvider;
    private readonly SemaphoreSlim _dialogSemaphore = new(1, 1);
    private static readonly string LogDirectory = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
        "HotPreview", "Logs");

    public GlobalExceptionHandler(UIContextProvider uiContextProvider)
    {
        _uiContextProvider = uiContextProvider;
    }

    /// <summary>
    /// Handles an unhandled exception by logging it and showing an error dialog to the user.
    /// </summary>
    /// <param name="exception">The exception that occurred</param>
    /// <param name="source">The source of the exception (e.g., "Application", "TaskScheduler", "AppDomain")</param>
    public async Task HandleUnhandledException(Exception exception, string source)
    {
        try
        {
            // Log the exception
            Log.Error(exception, "Unhandled exception from {Source}: {Message}", source, exception.Message);

            // Show error dialog to user (but prevent multiple dialogs from showing simultaneously)
            await ShowErrorDialogAsync(exception, source);
        }
        catch (Exception ex)
        {
            // If we can't even handle the exception properly, log it and continue
            Log.Fatal(ex, "Failed to handle unhandled exception from {Source}", source);
        }
    }

    /// <summary>
    /// Shows an error dialog to the user with exception details and log file information.
    /// </summary>
    private async Task ShowErrorDialogAsync(Exception exception, string source)
    {
        // Prevent multiple error dialogs from showing at once
        if (!await _dialogSemaphore.WaitAsync(100))
        {
            return; // Another dialog is already showing
        }

        try
        {
            // Switch to UI thread if needed
            if (_uiContextProvider.UIContext != null)
            {
                var tcs = new TaskCompletionSource<bool>();
                _uiContextProvider.UIContext.Post(async _ =>
                {
                    try
                    {
                        await ShowErrorDialogOnUIThread(exception, source);
                        tcs.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }, null);
                await tcs.Task;
            }
            else
            {
                // Fallback if UI context is not available
                await ShowErrorDialogOnUIThread(exception, source);
            }
        }
        finally
        {
            _dialogSemaphore.Release();
        }
    }

    /// <summary>
    /// Shows the error dialog on the UI thread.
    /// </summary>
    private async Task ShowErrorDialogOnUIThread(Exception exception, string source)
    {
        try
        {
            var dialog = new ErrorDialog(exception, source, LogDirectory);
            await dialog.ShowAsync();
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to show error dialog");

            // Fallback to basic message dialog if custom dialog fails
            try
            {
                var fallbackDialog = new ContentDialog
                {
                    Title = "Application Error",
                    Content = $"An unexpected error occurred:\n\n{exception.Message}\n\nPlease check the log files in:\n{LogDirectory}",
                    CloseButtonText = "OK",
                    XamlRoot = GetXamlRoot()
                };
                await fallbackDialog.ShowAsync();
            }
            catch
            {
                // If even the fallback fails, there's nothing more we can do
                Log.Fatal("Failed to show any error dialog to user");
            }
        }
    }

    /// <summary>
    /// Gets the XamlRoot for dialogs from the main window.
    /// </summary>
    private XamlRoot? GetXamlRoot()
    {
        try
        {
            if (App.CurrentMainWindow?.Content is FrameworkElement element)
            {
                return element.XamlRoot;
            }
        }
        catch
        {
            // Ignore errors getting XamlRoot
        }
        return null;
    }

    /// <summary>
    /// Opens the log directory in the file explorer.
    /// </summary>
    public static void OpenLogDirectory()
    {
        try
        {
            if (Directory.Exists(LogDirectory))
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = LogDirectory,
                    UseShellExecute = true
                });
            }
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Failed to open log directory: {LogDirectory}", LogDirectory);
        }
    }
}
